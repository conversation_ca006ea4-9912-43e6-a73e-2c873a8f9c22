# syntax=docker/dockerfile:1.4
FROM --platform=linux/amd64 ubuntu:20.04

ARG USERNAME="model"

# Remove default ubuntu user if exists and create model user
RUN if getent passwd ubuntu > /dev/null 2>&1; then \
        touch /var/mail/ubuntu 2>/dev/null || true; \
        chown ubuntu /var/mail/ubuntu 2>/dev/null || true; \
        userdel -r ubuntu 2>/dev/null || true; \
    fi

# Create model user with UID/GID 1000
RUN groupadd -g 1000 ${USERNAME} && \
    useradd -m -u 1000 -g 1000 ${USERNAME}

# Environment configuration
ENV PYTHONUNBUFFERED=1 \
    DEBIAN_FRONTEND=noninteractive

# Minimal system dependencies
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked \
    --mount=type=cache,target=/var/lib/apt,sharing=locked \
    apt-get update && \
    apt-get install -y --no-install-recommends \
    curl wget ca-certificates git \
    build-essential sudo zip unzip file bc \
    pkg-config libgl1 libglib2.0-0 \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Fix missing bits symlink for Ubuntu 20.04 header compatibility
RUN if [ -d "/usr/include/x86_64-linux-gnu/bits" ] && [ ! -e "/usr/include/bits" ]; then ln -sf /usr/include/x86_64-linux-gnu/bits /usr/include/bits; fi

# Install Miniforge3 for conda/mamba
RUN wget "https://github.com/conda-forge/miniforge/releases/latest/download/Miniforge3-Linux-x86_64.sh" && \
    bash Miniforge3-Linux-x86_64.sh -b -p /opt/conda && \
    rm Miniforge3-Linux-x86_64.sh

# Set conda paths
ENV PATH="/opt/conda/bin:$PATH"
ENV CONDA_ENVS_PATH=/opt/conda/envs
ENV MAMBA_ROOT_PREFIX=/opt/conda

# Initialize conda for all users
RUN /opt/conda/bin/conda init bash && \
    echo "source /opt/conda/etc/profile.d/conda.sh" >> /etc/bash.bashrc && \
    echo "source /opt/conda/etc/profile.d/mamba.sh" >> /etc/bash.bashrc

# Setup model user environment
RUN mkdir -p /home/<USER>/.config /home/<USER>/.cache /home/<USER>/.conda/pkgs && \
    chown -R ${USERNAME}:${USERNAME} /home/<USER>

# Initialize conda for model user
RUN echo "source /opt/conda/etc/profile.d/conda.sh" >> /home/<USER>/.bashrc && \
    echo "source /opt/conda/etc/profile.d/mamba.sh" >> /home/<USER>/.bashrc && \
    chown ${USERNAME}:${USERNAME} /home/<USER>/.bashrc

# Set model user environment variables
ENV HOME=/home/<USER>
ENV USER=${USERNAME}
ENV CONDA_PKGS_DIRS=/opt/conda/pkgs

# Create base directories
RUN mkdir -p /repo /workdir && \
    chown -R ${USERNAME}:${USERNAME} /home/<USER>/workdir

# Create model's Python environment
RUN mamba create -n python3.9 -y python=3.9 pip

# Ensure conda-forge strict priority
RUN conda config --add channels conda-forge && \
    conda config --set channel_priority strict && \
    conda install -y mamba -n base -c conda-forge

# Configure model user to auto-activate their environment
RUN echo "python3.9" > /home/<USER>/.conda/environments.txt && \
    echo "conda activate python3.9" >> /home/<USER>/.bashrc && \
    echo "conda activate python3.9" >> /home/<USER>/.bash_profile && \
    chown ${USERNAME}:${USERNAME} /home/<USER>/.bashrc /home/<USER>/.bash_profile

# Set default environment (for non-interactive sessions)
ENV CONDA_DEFAULT_ENV=python3.9
ENV PATH="/opt/conda/envs/python3.9/bin:$PATH"

# Fix permissions for model's conda environment
RUN chown -R ${USERNAME}:${USERNAME} /opt/conda/envs/python3.9 /opt/conda/pkgs

# Install common dependencies in model's environment
RUN if [ -n "requests wheel setuptools versioneer ninja hatchling maturin setuptools-rust dill ruff pyyaml pytest pytest-cov pytest-xdist hypothesis mypy cffi typing_extensions charset-normalizer pytz conda-build c-compiler cxx-compiler fortran-compiler" ]; then \
        mamba install -n python3.9 -c conda-forge -y requests wheel setuptools versioneer ninja hatchling maturin setuptools-rust dill ruff pyyaml pytest pytest-cov pytest-xdist hypothesis mypy cffi typing_extensions charset-normalizer pytz conda-build c-compiler cxx-compiler fortran-compiler; \
    fi && \
    if [ -n "" ]; then \
        /opt/conda/envs/python3.9/bin/pip install ; \
    fi

# directory creation and permissions
RUN mkdir -p /repo /tmp/files /opt/wheelhouse /workdir && \
    chown model:model /repo /workdir && \
    chmod 755 /repo /workdir /home/<USER>
    chmod 700 /tmp/files && \
    # Git config
    git config --global user.email '<EMAIL>' && \
    git config --global user.name 'Model User'

# REPOSITORY CLONING
RUN git clone https://github.com/numpy/numpy.git /repo && \
    cd /repo && \
    git config --global --add safe.directory /repo

# PROBLEM SPECIFIC DEPS
RUN cd /repo && \
    apt-get update && apt-get install -y libopenblas-dev && \
    mamba install -n python3.9 -y openblas cmake "setuptools=59.2.0" "wheel=0.38.1" && \
    mamba run -n python3.9 pip install "Cython>=0.29.30,<3.0" && \
    mamba install -n python3.9 gcc=11 gxx=11 gfortran=11 -y

# STATIC FILES (mounted at runtime under /opt/static_files/)
RUN mkdir -p /opt/static_files/

# REPOSITORY CHECKOUT
RUN cd /repo && \
    git checkout b862e4f4ec4b5d02b30a2f1b2ec9d1c9478b9977^ && \
    git submodule update --init --recursive && \
    git rev-parse HEAD > /tmp/base_commit.txt && \
    echo "Saved base commit: $(cat /tmp/base_commit.txt)"

# Install repository
RUN cd /repo && \
    /opt/conda/envs/python3.9/bin/pip install --no-deps --no-build-isolation .

# Rich test runner script baked into image
RUN echo '#!/bin/bash' > /run_tests.sh && \
    echo 'source /opt/conda/etc/profile.d/conda.sh' >> /run_tests.sh && \
    echo 'conda activate python3.9' >> /run_tests.sh && \
    echo 'cd /repo' >> /run_tests.sh && \
    echo 'echo "========================================="' >> /run_tests.sh && \
    echo 'echo "Running tests for: numpy-numpy-b862e4f"' >> /run_tests.sh && \
    echo 'echo "Repository: numpy/numpy"' >> /run_tests.sh && \
    echo 'echo "Commit: b862e4f4ec4b5d02b30a2f1b2ec9d1c9478b9977^"' >> /run_tests.sh && \
    echo 'echo "Python: $(python --version)"' >> /run_tests.sh && \
    echo 'echo "========================================="' >> /run_tests.sh && \
    echo 'echo ""' >> /run_tests.sh && \
    echo 'test_dir="/tmp/files/tests/numpy-numpy-b862e4f"' >> /run_tests.sh && \
    echo 'total=0; passed=0; failed=0' >> /run_tests.sh && \
    echo 'if [ -d "$test_dir" ]; then' >> /run_tests.sh && \
    echo '  for test in $(find $test_dir -name "*.py" | sort -V); do' >> /run_tests.sh && \
    echo '    if [ -f "$test" ]; then' >> /run_tests.sh && \
    echo '      total=$((total+1))' >> /run_tests.sh && \
    echo '      tname=$(basename "$test")' >> /run_tests.sh && \
    echo '      echo "[$total] Running: $tname"' >> /run_tests.sh && \
    echo '      python "$test" /tmp/dummy.txt --reference --file_prefix prep' >> /run_tests.sh && \
    echo '      code=$?' >> /run_tests.sh && \
    echo '      if [ $code -eq 0 ]; then echo "  ✓ PASSED"; passed=$((passed+1)); else echo "  ✗ FAILED (exit code: $code)"; failed=$((failed+1)); fi' >> /run_tests.sh && \
    echo '      echo ""' >> /run_tests.sh && \
    echo '    fi' >> /run_tests.sh && \
    echo '  done' >> /run_tests.sh && \
    echo '  echo "========================================="' >> /run_tests.sh && \
    echo '  echo "Results: $passed/$total passed, $failed failed"' >> /run_tests.sh && \
    echo '  echo "========================================="' >> /run_tests.sh && \
    echo 'else' >> /run_tests.sh && \
    echo '  echo "ERROR: No test directory found at $test_dir"; exit 1' >> /run_tests.sh && \
    echo 'fi' >> /run_tests.sh && \
    chmod +x /run_tests.sh

# Set environment variables
ENV BENCH_REPO="https://github.com/numpy/numpy.git" \
    BENCH_PROBLEM_ID="numpy-numpy-b862e4f" \
    LOG_LEVEL="DEBUG" \
    CONDA_DEFAULT_ENV=python3.9 \
    PATH="/opt/conda/envs/python3.9/bin:$PATH"

WORKDIR /workdir
CMD ["/bin/bash", "-c", "/run_tests.sh; exec bash"]
